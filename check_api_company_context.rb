#!/usr/bin/env ruby

# Load Rails environment
require_relative 'config/environment'

puts "🔍 Checking different company contexts..."
puts ""

current_user = User.find_by(email: '<EMAIL>')
puts "Current user: #{current_user.id} - #{current_user.email}"
puts ""

puts "=== Company 1 context (PRŮMYSLOVÉ ČIŠTĚNÍ s.r.o.) ==="
ActsAsTenant.with_tenant(Company.find(1)) do
  puts "Company: #{ActsAsTenant.current_tenant.id} - #{ActsAsTenant.current_tenant.name}"
  
  contract = ActsAsTenant.current_tenant.contracts.find_by(user_id: current_user.id)
  if contract
    puts "Found contract: #{contract.id} - #{contract.first_name} #{contract.last_name}"
    
    # Check if test events exist here
    events = Event.where(title: ['Test Illness Event', 'Test Vacation Event'])
    puts "Test events in this company: #{events.count}"
    events.each do |event|
      puts "  - #{event.title} (Contract: #{event.contract_id})"
    end
  else
    puts "No contract found for user in this company"
  end
end
puts ""

puts "=== Company 3 context (from JWT token) ==="
company_3 = Company.find(3)
ActsAsTenant.with_tenant(company_3) do
  puts "Company: #{ActsAsTenant.current_tenant.id} - #{ActsAsTenant.current_tenant.name}"
  
  contract = ActsAsTenant.current_tenant.contracts.find_by(user_id: current_user.id)
  if contract
    puts "Found contract: #{contract.id} - #{contract.first_name} #{contract.last_name}"
    
    # Check if there are any events for testing
    events = Event.all.limit(5)
    puts "Sample events in this company: #{events.count}"
    events.each do |event|
      puts "  - #{event.title} (Contract: #{event.contract_id}, Type: #{event.event_type}, Status: #{event.status})"
    end
  else
    puts "No contract found for user in this company"
  end
end
puts ""

puts "=== Contract 80 in Company 3? ==="
ActsAsTenant.with_tenant(company_3) do
  contract_80 = ActsAsTenant.current_tenant.contracts.find_by(id: 80)
  if contract_80
    puts "Contract 80 found in Company 3:"
    puts "  User ID: #{contract_80.user_id}"
    puts "  Name: #{contract_80.first_name} #{contract_80.last_name}"
    puts "  Email: #{contract_80.email}"
  else
    puts "Contract 80 NOT FOUND in Company 3"
  end
end