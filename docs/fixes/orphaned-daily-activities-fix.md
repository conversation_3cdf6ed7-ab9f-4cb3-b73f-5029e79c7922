# Orphaned Daily Activities Fix - TYM-22

**Date**: 2025-07-21  
**Linear Issue**: TYM-22  
**Priority**: Critical  
**Status**: ✅ Implemented

## Problem Summary

When users have Events (vacation/sick leave) scheduled for the next day but forget to close Daily Activities from the current day, orphaned activities remain open indefinitely. This creates serious data integrity problems that violate core business logic validations.

### Root Cause
The existing `DailyActivityManager::AutoCloser` only handles activities linked to specific daily logs:

```ruby
def find_open_activities
  DailyActivity.where(
    daily_log_id: @daily_log.id,  # Only finds linked activities
    end_time: nil
  )
end
```

**Missing Coverage:**
- Activities with `daily_log_id: nil` (orphaned activities)
- Activities from previous days that weren't properly closed
- Activities that remain open when no daily log is active

## Solution Implemented

### 1. OrphanedActivityCloser Service
**File**: `app/services/daily_activity_manager/orphaned_activity_closer.rb`

- Finds ALL open activities from previous days (regardless of daily_log_id)
- Closes them with appropriate end_time (end of activity day)
- Multi-tenant safe with proper company scoping
- Follows existing DailyActivityManager pattern

### 2. Daily CRON Job
**Endpoint**: `POST /cron/close_orphaned_activities`  
**Controller**: `app/controllers/cron_controller.rb`  
**Route**: Added to `config/routes.rb`

- Runs across all companies with proper tenant isolation
- Uses existing webhook authentication
- Returns count of closed activities for monitoring

### 3. Comprehensive Test Coverage
**File**: `spec/services/daily_activity_manager/orphaned_activity_closer_spec.rb`

- Tests orphaned activities (daily_log_id: nil)
- Tests activities with daily_log associations  
- Tests multi-tenant safety
- Tests multiple previous days handling
- 6 examples, 0 failures

## Implementation Details

### Service Logic
```ruby
def find_orphaned_activities
  query = DailyActivity.where(
    end_time: nil
  ).where(
    'start_time < ?', @current_date.beginning_of_day
  )
  
  # Add company scoping if current tenant is set
  if ActsAsTenant.current_tenant
    query = query.where(company: ActsAsTenant.current_tenant)
  end
  
  query
end
```

### CRON Endpoint
```ruby
def close_orphaned_activities
  closed_activities_count = 0
  
  Company.find_each do |company|
    ActsAsTenant.with_tenant(company) do
      closed_activities = DailyActivityManager::OrphanedActivityCloser.call
      closed_activities_count += closed_activities.size
    end
  end
  
  render json: { 
    status: "success", 
    message: "Orphaned activities cleanup completed",
    closed_activities: closed_activities_count
  }
end
```

## CRON Job Setup

To activate the daily cleanup, set up a CRON job on your hosting platform:

```bash
# Schedule: Run daily at 11:55 PM (before midnight to catch same-day orphans)
# Command:
curl -X POST https://your-app.onrender.com/cron/close_orphaned_activities \
  -H "webhook-signature: ${RENDER_WEBHOOK_SECRET}"
```

**Recommended Schedule**: `55 23 * * *` (11:55 PM daily)

## Key Benefits

1. **Prevents Data Integrity Issues**: Ensures no activities remain open from previous days
2. **Event System Compatibility**: Resolves conflicts when Events exist + Activities open + No Daily Log
3. **Multi-Tenant Safe**: Properly scoped for company isolation
4. **Comprehensive**: Handles both orphaned (daily_log_id: nil) and associated activities
5. **Monitoring**: Returns count of closed activities for operational visibility

## Files Modified/Created

### New Files:
- `app/services/daily_activity_manager/orphaned_activity_closer.rb`
- `spec/services/daily_activity_manager/orphaned_activity_closer_spec.rb`
- `docs/fixes/orphaned-daily-activities-fix.md`

### Modified Files:
- `app/controllers/cron_controller.rb` - Added close_orphaned_activities endpoint
- `config/routes.rb` - Added CRON route

## Testing

All tests pass:
```bash
bundle exec rspec spec/services/daily_activity_manager/orphaned_activity_closer_spec.rb
# 6 examples, 0 failures
```

Integration test:
```bash
bundle exec rails runner "DailyActivityManager::OrphanedActivityCloser.call"
# Returns array of closed activities
```

## Code Review & Improvements

### Gemini Code Review Analysis
After implementation, Gemini Code Review provided two suggestions:

1. **✅ IMPLEMENTED - Error Handling (Critical)**
   - Added begin/rescue around ActsAsTenant.with_tenant blocks
   - Prevents single company failure from stopping entire cleanup
   - Logs errors and continues processing other companies
   - Returns failed_companies array for monitoring

2. **❌ NOT IMPLEMENTED - Performance Optimization (High Priority)**
   - Suggestion: Use update_all instead of individual update! calls
   - **Decision**: Rejected due to callback dependency
   - **Analysis**: DailyActivity has `before_save :set_duration` callback
   - Using update_all would bypass this critical callback and break duration calculation
   - Daily cleanup job doesn't require real-time performance optimization

### Technical Decision
Prioritized **safety over optimization** following "do not overengineer" principle:
- Error handling provides critical reliability improvement
- Performance optimization would introduce subtle bugs
- Current approach is sufficient for daily background job

## Operational Notes

- **Scope**: Only affects activities with start_time < current day
- **Safety**: Never touches current day activities
- **Performance**: Uses safe individual updates preserving callbacks
- **Error Handling**: Robust error isolation per company with logging
- **Monitoring**: JSON response includes activity count and failed companies
- **Reliability**: Single company failures don't affect other companies

This fix resolves the critical data integrity issue described in TYM-22 while maintaining system stability and reliability.