# Playwright Testing Guide for AttendifyApp

## Overview

This document outlines the comprehensive Playwright testing setup for AttendifyApp, designed specifically for AI agent (Claude Code) testing with real development database data and multi-role authentication.

## Problem Statement & Solution

### Previous Issues (Fixed)
- **Server Hammering**: UI-based authentication caused circular server calls and timeouts
- **Authentication Failures**: Tests used non-existent `<EMAIL>` credentials  
- **No Test Isolation**: Tests relied on random database state
- **Performance Problems**: Every test performed full UI login (10+ seconds per test)

### Current Solution
- **API-Based Authentication**: Fast, reliable authentication without UI interaction
- **Multi-Role Testing**: Three distinct user roles with real development data
- **Real Data Testing**: Uses actual development database for realistic scenarios
- **Performance Optimized**: Authentication takes <1 second vs 10+ seconds

## User Roles & Access

### Test Users (Development Database)

#### Owner: `<EMAIL>`
- **Companies**: 
  - Company ID 98 (Plus Tier activated)  
  - Company ID 100 (Basic tier)
- **Permissions**: Full company management, all features
- **Use Cases**: Company settings, billing, user management, full feature testing

#### Admin: `<EMAIL>` 
- **Companies**: Company ID 98 (Plus Tier)
- **Permissions**: Work management, user management, cannot delete company
- **Use Cases**: Work assignment, employee management, administrative workflows

#### Employee: `<EMAIL>`
- **Companies**: Company ID 98 (Plus Tier)  
- **Permissions**: Basic work management, no administrative access
- **Use Cases**: Daily work logging, personal work assignments, employee workflows

## Authentication Fixtures

### Available Fixtures

```typescript
import { test, expect } from './fixtures/auth';

// Legacy fixture (uses owner credentials)
test('example', async ({ authenticatedPage }) => { });

// Role-specific fixtures  
test('owner test', async ({ authenticatedOwner }) => { });
test('admin test', async ({ authenticatedAdmin }) => { });
test('employee test', async ({ authenticatedEmployee }) => { });

// Unauthenticated
test('login test', async ({ loginPage }) => { });
```

### How Authentication Works

1. **CSRF Token Retrieval**: Gets token from `/users/sign_in` page
2. **API Login**: Posts credentials to `/users/sign_in` endpoint
3. **Verification**: Navigates to `/mainbox` and confirms no redirect to login
4. **Session Cookies**: Automatically maintained by Playwright's request context

This eliminates:
- ❌ UI form filling
- ❌ Button clicking  
- ❌ Wait for page loads
- ❌ Timeout issues
- ❌ Server overload

## Testing Patterns

### 1. Single Role Testing
```typescript
test('employee should see today\'s works', async ({ authenticatedEmployee }) => {
  const page = authenticatedEmployee.page;
  await page.goto('/mainbox');
  
  // Test employee-specific functionality
  await expect(page.locator('[data-vue-component="today-work-section"]')).toBeVisible();
  // Should NOT see admin panels
  await expect(page.locator('[data-testid="company-settings"]')).not.toBeVisible();
});
```

### 2. Multi-Role Workflows
```typescript
test('workflow: admin assigns work, employee completes it', async ({ browser }) => {
  // Admin context
  const adminContext = await browser.newContext();
  const adminPage = await adminContext.newPage();
  await authenticateViaApi(adminPage, adminPage.request(), testUsers.admin.email, testUsers.admin.password);
  
  // Admin creates work
  await adminPage.goto('/works/new');
  // ... admin actions ...
  
  // Employee context  
  const employeeContext = await browser.newContext();
  const employeePage = await employeeContext.newPage();
  await authenticateViaApi(employeePage, employeePage.request(), testUsers.employee.email, testUsers.employee.password);
  
  // Employee sees and completes work
  await employeePage.goto('/mainbox');
  // ... employee actions ...
  
  await adminContext.close();
  await employeeContext.close();
});
```

### 3. Permission Boundary Testing
```typescript
test('employee cannot access company settings', async ({ authenticatedEmployee }) => {
  const page = authenticatedEmployee.page;
  
  // Try to access admin endpoint
  await page.goto('/companies/98/edit');
  
  // Should be redirected or show error
  await expect(page.locator('text=Unauthorized')).toBeVisible();
});
```

### 4. Feature Toggle Testing (Plus Tier)
```typescript
test('plus tier features work correctly', async ({ authenticatedOwner }) => {
  const page = authenticatedOwner.page;
  
  // Test on plus tier company (ID 98)
  await page.goto('/mainbox'); // Should default to company 98
  await expect(page.locator('[data-testid="premium-feature"]')).toBeVisible();
  
  // Switch to basic company (ID 100)
  await page.selectOption('[data-testid="company-selector"]', '100');
  await expect(page.locator('[data-testid="premium-feature"]')).not.toBeVisible();
});
```

## Testing Strategy for Claude Code Agents

### When to Use Each Role

#### Owner Testing (`authenticatedOwner`)
- Company management features
- Billing and subscription testing  
- User role management
- Full application feature testing
- Multi-company scenarios

#### Admin Testing (`authenticatedAdmin`)
- Work management workflows
- Employee assignment testing
- Reporting and analytics
- Administrative UI components
- Permission testing (can do admin tasks, cannot do owner tasks)

#### Employee Testing (`authenticatedEmployee`)  
- Daily workflows (TYM-28 type issues)
- Work logging and time tracking
- Personal dashboard functionality
- Mobile-friendly UI testing
- Permission testing (cannot access admin features)

### Test Organization

```
test/e2e/
├── auth.spec.ts              # Authentication flows
├── owner/                    # Owner-specific tests
│   ├── company-management.spec.ts
│   ├── billing.spec.ts
│   └── user-management.spec.ts
├── admin/                    # Admin-specific tests  
│   ├── work-management.spec.ts
│   ├── employee-assignment.spec.ts
│   └── reporting.spec.ts
├── employee/                 # Employee-specific tests
│   ├── daily-workflows.spec.ts
│   ├── time-tracking.spec.ts
│   └── mobile-interface.spec.ts
├── multi-role/              # Cross-role workflows
│   ├── work-assignment-flow.spec.ts
│   └── communication-flow.spec.ts
└── permissions/             # Permission boundary tests
    ├── role-restrictions.spec.ts
    └── feature-access.spec.ts
```

## Best Practices for Claude Code

### 1. Always Specify User Context
```typescript
// ❌ Bad: Unclear which role is being tested
test('user can create work', async ({ authenticatedPage }) => {

// ✅ Good: Clear role context
test('admin can create work assignment', async ({ authenticatedAdmin }) => {
```

### 2. Test Business Logic, Not Just UI
```typescript
// ✅ Good: Tests actual business functionality
test('employee sees only today\'s and future works (TYM-28)', async ({ authenticatedEmployee }) => {
  // Verify API call
  const apiRequests = [];
  page.on('request', req => {
    if (req.url().includes('/api/v1/works/today')) apiRequests.push(req);
  });
  
  await page.goto('/mainbox');
  expect(apiRequests[0].url()).toContain('/api/v1/works/today');
  
  // Verify UI reflects correct data  
  await expect(page.locator('[data-vue-component="today-work-section"]')).toBeVisible();
});
```

### 3. Use Real Data Advantages
```typescript
// ✅ Leverage development database richness
test('works display correctly with real assignments', async ({ authenticatedEmployee }) => {
  await page.goto('/mainbox');
  
  // Development DB has real work data - test against it
  const workCards = page.locator('[data-testid="work-card"]');
  const count = await workCards.count();
  
  if (count > 0) {
    // Test with real data
    await expect(workCards.first()).toContainText(/.+/); // Has real title
    await expect(workCards.first().locator('[data-testid="location"]')).toBeVisible();
  }
});
```

### 4. Handle Different Scenarios
```typescript
test('handles both empty and populated states', async ({ authenticatedEmployee }) => {
  await page.goto('/mainbox');
  
  const workCards = page.locator('[data-testid="work-card"]');
  const count = await workCards.count();
  
  if (count === 0) {
    await expect(page.locator('text=Nemáte přiřazené práce na dnes')).toBeVisible();
  } else {
    await expect(workCards.first()).toBeVisible();
    // Test work interaction
    await workCards.first().locator('[data-testid="start-work"]').click();
  }
});
```

## Running Tests

### Development
```bash
# Run all tests
npm run test:e2e

# Run specific role tests
npx playwright test test/e2e/employee/

# Run with UI (for debugging)
npm run test:e2e:ui

# Run specific test file
npx playwright test test/e2e/works-today.spec.ts
```

### Debugging
```bash
# Debug mode (step through)
npm run test:e2e:debug

# Headed mode (see browser)
npx playwright test --headed

# Specific browser
npx playwright test --project=chromium
```

## Troubleshooting

### Authentication Issues
- **Error**: "Still on login page"
  - **Fix**: Check credentials in `test-data.ts` match development DB
  - **Debug**: Add `await page.screenshot()` before authentication

### Timeout Issues  
- **Error**: Test timeouts waiting for elements
  - **Fix**: Increase timeout: `await expect(element).toBeVisible({ timeout: 10000 })`
  - **Debug**: Check if development server is running on port 5100

### Data-Dependent Failures
- **Error**: "Expected element not found"
  - **Fix**: Handle both empty and populated data states
  - **Use**: Conditional testing based on actual data presence

## Future Enhancements

### Planned Features
1. **API Test Data Management**: Create/cleanup test data via API
2. **Mobile Testing**: Responsive design validation  
3. **Performance Testing**: Page load timing verification
4. **Accessibility Testing**: WCAG compliance checks
5. **Visual Regression**: Screenshot comparisons

### Integration Points
- **CI/CD**: Automated testing in deployment pipeline
- **Monitoring**: Real user behavior replication
- **Documentation**: Auto-generated test coverage reports

## Summary

This testing setup provides:
- ✅ **Fast, reliable authentication** (API-based)
- ✅ **Real data testing** (development database)  
- ✅ **Multi-role coverage** (owner/admin/employee)
- ✅ **AI agent friendly** (clear patterns, good error handling)
- ✅ **Business logic focus** (not just UI clicking)

Perfect for comprehensive testing by Claude Code agents with realistic, production-like scenarios while maintaining performance and reliability.