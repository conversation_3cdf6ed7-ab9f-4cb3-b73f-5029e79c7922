# 🚨 CRITICAL BUG REPORT: invitation_accepted_at Never Set

**Bug ID**: invitation_accepted_at_missing  
**Severity**: CRITICAL - Blocks user login after invitation acceptance  
**Date Found**: 2025-07-22  
**Status**: ACTIVE BUG  

## Bug Description

**The `InvitationService.accept_company_connection` method never sets `invitation_accepted_at` timestamp, preventing users from logging in even after successfully accepting invitations.**

## Root Cause Analysis

### JWT Login Requirement
The JWT authentication in `app/controllers/api/v1/auth_controller.rb:52-53` requires:
```ruby
unless user.confirmed_at.present? || user.invitation_accepted_at.present?
  # Login blocked with 403 Forbidden
```

### Invitation Workflow Gap
In `app/services/invitation_service.rb:221-262`, the `accept_company_connection` method:

✅ **What it does:**
- Creates `CompanyUserRole` 
- Clears invitation token
- Returns success

❌ **What it's missing:**
```ruby
# MISSING: Set invitation accepted timestamp
user.update!(invitation_accepted_at: Time.current)
```

## User Impact

### Broken User Journey
1. ✅ User receives invitation email
2. ✅ User clicks invitation link  
3. ✅ System creates company connection
4. ✅ User gets "invitation accepted" success message
5. ❌ **User tries to login → 403 Forbidden "E-mail nebyl potvrzen"**

### Real User Experience
- Users believe they've successfully accepted invitations
- Users cannot log in to access the system
- Error message is misleading (suggests email confirmation issue)
- Users stuck in "invitation limbo" state

## Technical Evidence

### Employee User State (Test Case)
```ruby
# After "successful" invitation acceptance
user = User.find_by(email: "<EMAIL>")
user.confirmed_at         # => nil (Devise confirmable disabled)  
user.invitation_accepted_at # => nil (NEVER SET BY INVITATION SERVICE)

# Result: Login fails with 403 Forbidden
```

### Code Analysis
**File**: `app/services/invitation_service.rb`  
**Method**: `accept_company_connection` (lines 221-262)  
**Missing Code**:
```ruby
# This should be added after line 243:
user.update!(invitation_accepted_at: Time.current)
```

## System Architecture Context

### Devise Configuration
From `app/models/user.rb:6-8`:
```ruby
# Removed session-dependent modules: :invitable, :recoverable, :confirmable, :rememberable
# These are replaced with custom JWT-based implementations for PWA compatibility
```

### Authentication Logic  
The system uses **EITHER** confirmation approach:
- `confirmed_at` (for direct registration, disabled for invitations)
- `invitation_accepted_at` (for invited users, **NOT BEING SET**)

## Investigation History

### Previous Fix Attempt (TYM-38)
The system had a related fix for "existing-user-invitation-fix" that addressed JWT login logic to accept `invitation_accepted_at`. However, the underlying bug of **never setting the timestamp** was missed.

### Testing Methodology Error
During testing, the symptom (`invitation_accepted_at: nil`) was manually fixed by setting `confirmed_at` in the database, which **bypassed discovering this critical bug**.

## Required Fix

### Primary Fix (CRITICAL)
In `app/services/invitation_service.rb`, add after line 243:
```ruby
# Set invitation accepted timestamp for JWT login compatibility
user.update!(invitation_accepted_at: Time.current)
Rails.logger.info "[INVITATION] Set invitation_accepted_at for user #{user.id}"
```

### Fix Validation Steps
1. Reset employee user: `user.update(confirmed_at: nil, invitation_accepted_at: nil)`
2. Generate invitation token and test acceptance workflow  
3. Verify `invitation_accepted_at` gets set
4. Verify user can successfully log in after acceptance

## Prevention Measures

### Testing Requirements
- **Never manually fix database state** to bypass workflow issues
- **Always test complete user journeys** end-to-end
- **Verify authentication requirements** are met by workflows

### Code Review Checkpoints
- Invitation acceptance should set required authentication timestamps
- JWT login requirements should be documented and validated
- User state transitions should be complete and consistent

## Files Affected

**Primary Fix Required:**
- `app/services/invitation_service.rb` - Add `invitation_accepted_at` timestamp

**Testing Files:**
- Test invitation workflow end-to-end
- Verify JWT login works after acceptance

**Documentation:**
- Update invitation workflow documentation
- Document JWT authentication requirements

## Business Impact

### Current State
- **ALL invited users cannot log in** after accepting invitations
- Users experience broken registration workflow
- Support tickets for "login not working" after invitations

### Post-Fix State  
- Invited users can log in immediately after accepting invitations
- Consistent user experience across all authentication paths
- Reduced support burden from login failures

---

**CRITICAL**: This bug affects every user who goes through the invitation workflow. The fix is simple but essential for invitation functionality.

**Next Actions**: 
1. Implement the one-line fix in InvitationService
2. Test invitation workflow end-to-end  
3. Verify JWT authentication works properly
4. Deploy fix to resolve user login issues