#!/usr/bin/env ruby

# Load Rails environment
require_relative 'config/environment'

puts "🔍 Investigating contract ID mismatch..."
puts "API finds contract 80 for current user, but our check finds contract 191"
puts ""

ActsAsTenant.with_tenant(Company.first) do
  current_user = User.find_by(email: '<EMAIL>')
  puts "Current user: #{current_user.id} - #{current_user.email}"
  puts "Company: #{Company.first.id} - #{Company.first.name}"
  puts ""
  
  puts "=== All contracts for current user (company scoped) ==="
  contracts = Company.first.contracts.where(user_id: current_user.id)
  puts "Found #{contracts.count} contracts:"
  contracts.each do |contract|
    puts "  ID: #{contract.id} - #{contract.first_name} #{contract.last_name} - #{contract.email} - Company: #{contract.company_id}"
  end
  puts ""
  
  puts "=== Direct Contract.find_by check (like API does) ==="
  api_contract = Company.first.contracts.find_by(user_id: current_user.id)
  puts "API finds contract: #{api_contract&.id || 'NONE'}"
  puts ""
  
  if api_contract
    puts "API contract details:"
    puts "  ID: #{api_contract.id}"
    puts "  Name: #{api_contract.first_name} #{api_contract.last_name}"
    puts "  Email: #{api_contract.email}"
    puts "  Company: #{api_contract.company_id}"
    puts "  Created: #{api_contract.created_at}"
    puts ""
  end
  
  puts "=== Check if there are multiple contracts with same user_id ==="
  all_user_contracts = Company.first.contracts.where(user_id: current_user.id).order(:id)
  puts "All contracts for user #{current_user.id}:"
  all_user_contracts.each_with_index do |contract, index|
    puts "  #{index + 1}. ID: #{contract.id} - #{contract.first_name} #{contract.last_name} - Created: #{contract.created_at}"
  end
  puts ""
  
  puts "=== Check if contract 80 exists and its details ==="
  contract_80 = Company.first.contracts.find_by(id: 80)
  if contract_80
    puts "Contract 80 found:"
    puts "  ID: #{contract_80.id}"
    puts "  User ID: #{contract_80.user_id}"
    puts "  Name: #{contract_80.first_name} #{contract_80.last_name}"
    puts "  Email: #{contract_80.email}"
    puts "  Company: #{contract_80.company_id}"
  else
    puts "Contract 80 NOT FOUND"
  end
end