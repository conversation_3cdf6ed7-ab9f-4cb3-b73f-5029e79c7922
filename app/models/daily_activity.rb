class DailyActivity < ApplicationRecord
  belongs_to :user
  belongs_to :company
  belongs_to :contract, optional: true
  belongs_to :daily_log, optional: true
  belongs_to :work, optional: true
  belongs_to :work_assignment, optional: true

  has_one :work_session, dependent: :nullify

  before_save :set_duration

  # Activity types
  ACTIVITY_TYPES = %w[travel_to_work work_at_location work_remote break regular].freeze

  validates :activity_type, inclusion: { in: ACTIVITY_TYPES }, allow_nil: true
  validate :require_running_daily_log, on: :create
  
  def self.force_close_work_activities(work_id, reason)
    where(work_id: work_id, end_time: nil).update_all(
      end_time: Time.current
    )
  end

  def as_json(options={})
    super(options.merge(methods: [:duration_in_text]))
  end

  def duration_in_text
    return nil unless start_time && end_time

    total_seconds = (end_time - start_time).to_i
    return "#{total_seconds} s" if total_seconds < 60

    total_minutes = total_seconds / 60
    hours = total_minutes / 60
    minutes = total_minutes % 60

    if hours > 0
      "#{hours} hod. #{minutes} min."
    else
      "#{minutes} min."
    end
  end

  private

  def set_duration
    if self.end_time.present?
      self.duration = (self.end_time - self.start_time).to_i
    end
  end

  def require_running_daily_log
    return unless daily_log_id.present?

    # Check if the daily log exists and is currently running (no end_time)
    daily_log = DailyLog.find_by(id: daily_log_id, user: user, company: company)

    unless daily_log
      errors.add(:daily_log, 'Denní záznam nebyl nalezen.')
      return
    end

    if daily_log.end_time.present?
      errors.add(:base, 'Nelze vytvořit aktivitu. Denní záznam práce již byl ukončen.')
      return
    end

    # Check if the daily log is for today
    today = Date.current
    unless daily_log.start_time.to_date == today
      errors.add(:base, 'Nelze vytvořit aktivitu. Denní záznam není pro dnešní den.')
    end
  end

end