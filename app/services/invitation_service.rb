class InvitationService
  class InvitationError < StandardError; end
  
  def self.send_invitation(email:, first_name:, last_name:, sender:, company:)
    new.send_invitation(
      email: email,
      first_name: first_name,
      last_name: last_name,
      sender: sender,
      company: company
    )
  end
  
  def self.validate_token(token)
    new.validate_token(token)
  end
  
  def self.accept_invitation(token:, user:)
    new.accept_invitation(token: token, user: user)
  end
  
  def initialize
    # Service instance methods
  end
  
  def send_invitation(email:, first_name:, last_name:, sender:, company:)
    # Check if user already exists and is connected
    existing_user = User.find_by(email: email)
    
    if existing_user
      return handle_existing_user_invitation(existing_user, first_name, last_name, sender, company)
    else
      return handle_new_user_invitation(email, first_name, last_name, sender, company)
    end
  end
  
  def validate_token(token)
    # Database-only invitation validation
    invitation = Invitation.pending.not_expired.find_by(unique_token: token)
    
    unless invitation
      Rails.logger.warn "[SECURITY] Invalid or expired invitation token: #{token[0..8]}..."
      return nil
    end
    
    Rails.logger.info "[SECURITY] Valid invitation token validated for #{invitation.email}"
    
    # Return hash format compatible with existing code
    {
      'email' => invitation.email,
      'company_id' => invitation.company_id,
      'first_name' => invitation.first_name,
      'last_name' => invitation.last_name,
      'sender_id' => invitation.sender_id,
      'created_at' => invitation.created_at.iso8601,
      'invitation_id' => invitation.id
    }
  end
  
  def accept_invitation(token:, user:)
    invitation_data = validate_token(token)
    return { success: false, error: 'Invalid or expired invitation token' } unless invitation_data
    
    company_id = invitation_data['company_id']
    sender_id = invitation_data['sender_id']
    email = invitation_data['email']
    
    # Validate that the user accepting matches the invitation
    unless user.email == email
      Rails.logger.warn "[SECURITY] User email mismatch: #{user.email} vs invitation #{email}"
      return {
        success: false,
        error: 'User email does not match invitation email',
        details: { email: 'does not match invitation' }
      }
    end
    
    accept_company_connection(user, company_id, sender_id, token)
  end
  
  def complete_user_registration(email:, company_id:, sender_id:, invitation_token:, password:, password_confirmation:)
    # Validate passwords
    unless password.present? && password_confirmation.present?
      return {
        success: false,
        error: 'Password and password confirmation are required',
        details: { password: 'is required', password_confirmation: 'is required' }
      }
    end
    
    unless password == password_confirmation
      return {
        success: false,
        error: 'Password confirmation does not match',
        details: { password_confirmation: 'does not match password' }
      }
    end
    
    # Create new user with only email and password - no first_name/last_name complications
    user = User.new(
      email: email,
      password: password,
      password_confirmation: password_confirmation
    )
    
    if user.save
      # Accept company connection
      result = accept_company_connection(user, company_id, sender_id, invitation_token)
      
      Rails.logger.info "[INVITATION] New user registration completed: user #{user.id} created and joined company #{company_id}"
      
      {
        success: true,
        message: 'Registration completed successfully',
        user: user
      }
    else
      {
        success: false,
        error: 'Registration failed',
        details: user.errors.messages
      }
    end
  rescue => e
    Rails.logger.error "[ERROR] Failed to complete user registration: #{e.message}"
    {
      success: false,
      error: 'Failed to complete registration',
      details: { registration: 'could not be processed' }
    }
  end
  
  private
  
  def handle_existing_user_invitation(user, first_name, last_name, sender, company)
    # Check if user is already connected to this company
    existing_role = user.company_user_roles.find_by(company: company)
    
    if existing_role
      return {
        success: false,
        error: 'User is already connected to this company',
        details: { email: 'is already a member of this company' }
      }
    end
    
    # FIXED: No JWT token needed for existing users - just send notification email
    # They will login normally and see pending contracts via /api/v1/company_connections/fetch
    
    # Send simple notification email
    CompanyConnectionMailer.existing_user_notification(
      sender: sender,
      user: user,
      company: company,
      contract: nil  # No contract for direct API invitations
    ).deliver_now
    
    Rails.logger.info "[INVITATION] Notification sent to existing user #{user.email} for company #{company.id}"
    
    {
      success: true,
      message: 'Notification sent to existing user successfully',
      type: 'existing_user'
    }
  rescue => e
    Rails.logger.error "[ERROR] Failed to send notification to existing user: #{e.message}"
    {
      success: false,
      error: 'Failed to send notification email',
      details: { email: 'notification could not be sent' }
    }
  end
  
  def handle_new_user_invitation(email, first_name, last_name, sender, company)
    # Generate invitation token
    invitation_token = generate_invitation_token(email, company.id, first_name, last_name, sender.id)
    
    # Send new user invitation email
    CompanyConnectionMailer.new_user_invitation(
      sender: sender,
      email: email,
      company: company,
      contract: nil,  # No contract for direct API invitations
      token: invitation_token
    ).deliver_now
    
    Rails.logger.info "[INVITATION] New user invitation sent to #{email} for company #{company.id}"
    
    {
      success: true,
      message: 'Invitation sent successfully',
      type: 'new_user'
    }
  rescue => e
    Rails.logger.error "[ERROR] Failed to send new user invitation: #{e.message}"
    {
      success: false,
      error: 'Failed to send invitation email',
      details: { email: 'invitation could not be sent' }
    }
  end
  
  def generate_invitation_token(email, company_id, first_name, last_name, sender_id)
    # Use database-based invitation creation with race condition protection
    company = Company.find(company_id)
    sender = User.find(sender_id)
    
    invitation = Invitation.create_jwt_invitation!(
      email: email,
      company: company,
      sender: sender,
      first_name: first_name,
      last_name: last_name
    )
    
    Rails.logger.info "[SECURITY] Invitation token generated for #{email} to company #{company_id} (ID: #{invitation.id})"
    
    invitation.unique_token
  end
  
  def accept_company_connection(user, company_id, sender_id, invitation_token)
    company = Company.find(company_id)
    
    # Check if user is already connected to this company
    existing_role = user.company_user_roles.find_by(company: company)
    
    if existing_role
      clear_invitation_token(invitation_token, user.email, company_id)
      return {
        success: false,
        error: 'You are already connected to this company',
        details: { company: 'connection already exists' }
      }
    end
    
    # Create company user role (default to employee role)
    employee_role = Role.find_by(name: 'employee') || Role.create!(name: 'employee')
    
    company_user_role = user.company_user_roles.create!(
      company: company,
      role: employee_role,
      active: true
    )
    
    # Set invitation accepted timestamp for JWT login compatibility
    user.update!(invitation_accepted_at: Time.current)
    
    # Clear invitation token
    clear_invitation_token(invitation_token, user.email, company_id)
    
    Rails.logger.info "[INVITATION] Company connection accepted: user #{user.id} joined company #{company_id}"
    
    {
      success: true,
      message: 'Company connection accepted successfully',
      user: user
    }
  rescue => e
    Rails.logger.error "[ERROR] Failed to accept company connection: #{e.message}"
    {
      success: false,
      error: 'Failed to accept company connection',
      details: { invitation: 'could not be processed' }
    }
  end
  
  def clear_invitation_token(token, email, company_id)
    begin
      # Database-only invitation cleanup
      invitation = Invitation.find_by(unique_token: token)
      if invitation
        invitation.update!(status: 'accepted', accepted_at: Time.current)
        Rails.logger.info "[SECURITY] Invitation #{invitation.id} marked as accepted for #{email}"
      else
        Rails.logger.warn "[WARNING] Invitation not found for token cleanup: #{token[0..8]}..."
      end
    rescue => e
      Rails.logger.warn "[WARNING] Failed to update invitation status: #{e.message}"
    end
  end
end