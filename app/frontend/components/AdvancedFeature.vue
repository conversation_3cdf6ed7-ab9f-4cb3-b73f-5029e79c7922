<template>
  <div>
    <div v-if="loading" class="loading-container">
      <div class="loading-spinner"></div>
    </div>
    <slot v-else-if="hasAccess"></slot>
    <div v-else class="advanced-lock">
      <div class="advanced-lock-icon">
        <i class="fa fa-lock"></i>
      </div>
      <div class="advanced-lock-content">
        <h3>{{ title }}</h3>
        <p>{{ description || $t('advanced_feature.default_description', { planName: requiredPlan }, 'Tato funkce je k dispozici pouze v tarifu {planName}.') }}</p>
        <div v-if="isManager" class="cta-buttons">
          <button @click="startTrial" class="btn btn-primary">{{ $t('advanced_feature.try_plus_free_30_days', 'Vyzkoušet Plus na 30 dní zdarma') }}</button>
          <a :href="upgradeLink" class="btn btn-secondary">{{ $t('activate', 'Aktivovat') }}</a>
        </div>
        <div v-else class="trial-info">
          <p>{{ $t('advanced_feature.contact_manager_for_activation_intro', 'Pro aktivaci této funkce se obraťte na svého manažera nebo administratora.') }}
          {{ $t('advanced_feature.you_can_also_create', 'Můžete si také založit') }}
          <LocalizedLink :to="'/companies'" class="text-link-action blue" :use-anchor="true">{{ $t('advanced_feature.own_workspace_link_text', 'vlastní pracovní prostor') }}</LocalizedLink>
          {{ $t('advanced_feature.and_try_plus_free_30_days', 'a vyzkoušet Plus na 30 dní zdarma.') }}</p>
        </div>
        <p class="trial-info">{{ $t('advanced_feature.no_card_needed_no_payment', 'Bez nutnosti zadání platební karty. Žádná platba nebude požadována.') }}</p>
      </div>
    </div>

    <!-- Confirmation Dialog -->
    <div v-if="showConfirmation" class="confirmation-dialog">
      <div class="dialog-content">
        <h3>{{ $t('advanced_feature.trial_confirmation_title', 'Potvrzení zkušební verze') }}</h3>
        <p>{{ $t('advanced_feature.confirm_start_plus_trial_30_days_q', 'Opravdu chcete začít 30denní zkušební verzi tarifu Plus?') }}</p>
        <p class="trial-info">{{ $t('advanced_feature.no_card_needed_no_payment', 'Bez nutnosti zadání platební karty. Žádná platba nebude požadována.') }}</p>
        <div class="dialog-buttons">
          <button @click="confirmTrial" class="btn btn-primary">{{ $t('advanced_feature.yes_start_trial', 'Ano, začít zkušební verzi') }}</button>
          <button @click="cancelTrial" class="btn btn-outline">{{ $t('cancel', 'Zrušit') }}</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import authorizationMixin from '../mixins/authorizationMixin';
import axios from 'axios';
import LocalizedLink from './LocalizedLink.vue';

export default {
  name: 'AdvancedFeature',
  mixins: [authorizationMixin],
  components: {
    LocalizedLink
  },
  props: {
    title: {
      type: String,
      default: 'Pokročilá funkce'
    },
    feature: {
      type: String,
      required: true
    },
    description: {
      type: String,
      default: ''
    },
    requiredPlan: {
      type: String,
      default: 'Plus'
    },
    upgradeLink: {
      type: String,
      default: 'https://tymbox.cz/tymbox-plus'
    }
  },
  data() {
    return {
      loading: true,
      hasAccess: false,
      featurePlan: null, // Renamed to avoid conflict with mixin
      error: null,
      showConfirmation: false
    };
  },
  mounted() {
    this.checkFeatureAccess();
  },
  methods: {
    startTrial() {
      this.showConfirmation = true;
    },
    cancelTrial() {
      this.showConfirmation = false;
    },
    async confirmTrial() {
      try {
        await axios.post('/subscriptions/activate_trial');
        this.showConfirmation = false;
        this.checkFeatureAccess();
      } catch (error) {
        console.error('Error starting trial', error);
        this.error = this.$t('advanced_feature.error_starting_trial', 'Nepodařilo se spustit zkušební verzi');
      }
    },
    async checkFeatureAccess() {
      try {
        this.loading = true;
        
        if (window.advancedFeatureStatus) {
          this.processSubscriptionData(window.advancedFeatureStatus);
          return;
        }

        const response = await axios.get('/api/v1/subscription_status');
        
        const responseData = response.data || {};
        
        window.advancedFeatureStatus = {
          current_plan: responseData.current_plan || 'free',
          available_features: responseData.available_features || [],
          subscription_active: responseData.subscription_active || false
        };
        
        this.processSubscriptionData(window.advancedFeatureStatus);
      } catch (error) {
        console.error('Error checking feature access', error);
        this.error = this.$t('advanced_feature.error_checking_feature_access', 'Nepodařilo se ověřit dostupnost funkce');
        this.hasAccess = false;
      } finally {
        this.loading = false;
      }
    },
    processSubscriptionData(data) {
      this.featurePlan = data.current_plan;
      
      const planLower = data.current_plan?.toLowerCase();
      
      if (this.feature === 'booking' || this.feature === 'reservation') {
        this.hasAccess = ['plus', 'premium'].includes(planLower);
      } else if (this.feature === 'meeting') {
        this.hasAccess = ['plus', 'premium'].includes(planLower);
      } else if (this.feature === 'work_planning') {
        this.hasAccess = ['plus', 'premium'].includes(planLower);
      } else if (this.feature === 'advanced_reporting') {
        this.hasAccess = planLower === 'premium';
      } else {
        this.hasAccess = data.available_features?.some(
          f => f.toLowerCase() === this.feature.toLowerCase()
        );
      }
      
      // Emit event to notify parent component
      this.$emit('access-determined', this.hasAccess);
    }
  }
};
</script>

<style>
.advanced-lock {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  background: #f9f9f9;
  border-radius: 8px;
  padding: 2rem;
  margin: 1rem 0;
  border: 1px solid #e0e0e0;
}

.advanced-lock-icon {
  font-size: 3rem;
  color: #ccc;
  margin-bottom: 1rem;
}

.advanced-lock-content {
  max-width: 400px;
}

.advanced-lock-content h3 {
  margin-bottom: 0.5rem;
  color: #333;
}

.advanced-lock-content p {
  margin-bottom: 1.5rem;
  color: #666;
}

.loading-container {
  display: flex;
  justify-content: center;
  padding: 2rem;
}

.loading-spinner {
  width: 30px;
  height: 30px;
  border: 3px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: #3498db;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.cta-buttons {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1rem;
}

.trial-info {
  font-size: 0.9rem;
  color: #666;
  margin-top: 0.5rem;
}

.confirmation-dialog {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.dialog-content {
  background: white;
  padding: 2rem;
  border-radius: 8px;
  max-width: 400px;
  width: 90%;
  text-align: center;
}

.dialog-buttons {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: 1.5rem;
}

.btn-secondary {
  background-color: #f0f0f0;
  color: #333;
  border: 1px solid #ddd;
}

.btn-secondary:hover {
  background-color: #e0e0e0;
}
</style>