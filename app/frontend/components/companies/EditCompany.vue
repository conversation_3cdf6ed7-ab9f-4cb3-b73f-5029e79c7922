<template>
  <div class="flex flex-col gap-y-2"> <!-- Vertical gap -->

    <!-- Logo Section - Shown only when editing -->
    <div v-if="!isNew" class="form-section">
      <h2 class="text-lg font-semibold mb-4">{{ $t('logo', 'Logo') }}</h2>
      <div class="flex flex-col md:flex-row gap-4 items-start">
        <!-- Logo Display Area -->
        <div class="flex-shrink-0 w-24 h-24 border border-gray-300 flex items-center justify-center overflow-hidden rounded">
          <img v-if="logoUrl" :src="logoUrl" :alt="$t('companies.company_logo_alt', 'Company Logo')" class="max-w-full max-h-full object-contain">
          <div v-else class="text-xl font-bold text-gray-400 text-center">
            {{ company.name ? company.name.substring(0, 3).toUpperCase() : $t('logo', 'LOGO') }}
          </div>
        </div>

        <!-- Logo Upload Form -->
        <div class="flex-grow">
          <form @submit.prevent="uploadLogo">
            <div class="form-group">
              <label for="company_logo_input" class="form-label">{{ $t('companies.upload_new_logo', 'Nahrát nové logo') }}</label>
              <input type="file" id="company_logo_input" @change="handleFileChange" accept="image/*" class="form-file-input">
            </div>
            <div class="form-group">
              <button type="submit" class="btn btn-primary" :disabled="!selectedFile">{{ $t('upload', 'Nahrát') }}</button>
            </div>
            <div v-if="uploadError" class="form-error-message">{{ uploadError }}</div>
          </form>
          <!-- Link to original logo -->
          <div v-if="originalLogoUrl" class="mt-2 text-sm">
            <a :href="originalLogoUrl" target="_blank" rel="noopener noreferrer" class="text-blue-600 hover:underline">{{ $t('companies.show_original_logo', 'Zobrazit originální logo') }}</a>
          </div>
        </div>
      </div>
    </div>

    <hr v-if="!isNew" class="border-gray-200" /> <!-- Divider only when editing -->

    <!-- Company Information Section -->
    <div class="form-section">
       <h2 class="text-lg font-semibold mb-4">{{ $t('companies.workspace_information_title', 'Informace o pracovním prostoru') }}</h2>
       <form @submit.prevent="handleSubmit"> <!-- Changed to handleSubmit -->
         <div class="gap-x-4"> <!-- Grid layout for info -->
            <div class="form-group">
              <label for="company_name" class="form-label">{{ $t('name', 'Název') }}</label>
              <input id="company_name" v-model="company.name" type="text" class="form-input" autocomplete="name" :placeholder="$t('name', 'Název')" autocorrect="off" autocapitalize="off" required> <!-- Added required -->
            </div>

            <div class="form-group">
              <label for="company_address" class="form-label">{{ $t('address', 'Adresa') }}</label>
              <input id="company_address" v-model="company.address" type="text" class="form-input" autocomplete="address" :placeholder="$t('address', 'Adresa')" autocorrect="off" autocapitalize="off">
            </div>

            <div class="form-group">
              <label for="company_phone" class="form-label">{{ $t('phone', 'Telefon') }}</label>
              <input id="company_phone" v-model="company.phone" type="text" class="form-input" autocomplete="phone" :placeholder="$t('phone', 'Telefon')" autocorrect="off" autocapitalize="off">
            </div>

            <div class="form-group">
              <label for="company_web" class="form-label">{{ $t('web', 'Web') }}</label>
              <input id="company_web" v-model="company.web" type="text" class="form-input" autocomplete="web" :placeholder="$t('web', 'Web')" autocorrect="off" autocapitalize="off">
            </div>
         </div> <!-- End grid -->

          <div class="form-group mt-2"> <!-- Description spans full width -->
            <label for="company_description" class="form-label">{{ $t('description', 'Popis') }}</label>
            <textarea id="company_description" v-model="company.description" class="form-textarea" :placeholder="$t('description', 'Popis')"></textarea>
          </div>

          <div class="mt-4 flex justify-end">
             <!-- Button text changes based on isNew -->
            <button type="submit" class="btn btn-primary">
                {{ isNew ? $t('companies.create_workspace_button', 'Vytvořit pracovní prostor') : $t('companies.save_information_button', 'Uložit informace') }}
            </button>
          </div>
        </form>
    </div>

    <hr v-if="!isNew" class="border-gray-200" /> <!-- Divider only when editing -->

    <!-- Company Settings Section - Shown only when editing -->
    <div v-if="!isNew" class="form-section">
      <h2 class="text-lg font-semibold mb-4">{{ $t('settings', 'Nastavení') }}</h2>
       <form @submit.prevent="updateCompanySettings">
          <div class="gap-x-4"> <!-- Grid layout for settings -->
            <div class="form-group">
              <label for="break_duration" class="form-label">{{ $t('companies.break_duration_label', 'Délka přestávky (minuty)') }}</label>
              <input v-model.number="companySettings.break_duration" type="number" id="break_duration" class="form-input w-full md:w-auto"> <!-- Adjust width -->
            </div>

            <!-- Placeholder for other potential grid items -->
             <div></div>

            <div class="form-checkbox-group">
              <input v-model="companySettings.approve_vacations" type="checkbox" id="approve_vacations" class="form-checkbox">
              <label for="approve_vacations" class="form-checkbox-label">{{ $t('companies.approve_vacations_label', 'Schvalování dovolené') }}</label>
            </div>

             <div class="form-checkbox-group">
              <input :disabled="!statusEmails" v-model="companySettings.daily_team_reports" type="checkbox" id="daily_team_reports" class="form-checkbox">
              <label for="daily_team_reports" class="form-checkbox-label" :class="{ 'text-gray-400 cursor-not-allowed': !statusEmails }">{{ $t('companies.daily_team_reports_label', 'Tým v práci na e-mail') }}</label>
            </div>

          </div> <!-- End grid -->

          <!-- Uncommented settings if needed later
          <div class="form-group">
            <label for="timezone" class="form-label">Časové pásmo</label>
            <input v-model="companySettings.timezone" type="text" id="timezone" class="form-input">
          </div>
          -->

          <div class="mt-4 flex justify-end">
            <button type="submit" class="btn btn-primary">{{ $t('companies.update_settings_button', 'Aktualizovat nastavení') }}</button>
          </div>
        </form>
    </div>

  </div>
</template>

<script>
import axios from 'axios';
import authorizationMixin from '../../mixins/authorizationMixin';

export default {
  name: 'CompanyForm', // Renamed conceptually
  mixins: [authorizationMixin],
  props: {
    companyId: {
      type: [Number, String],
      required: false, // No longer required
      default: null
    },
    isNew: { // New prop to indicate create mode
      type: Boolean,
      default: false
    },
    statusEmails: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      company: { 
        name: '',
        address: '',
        phone: '',
        web: '',
        description: ''
      },
      companySettings: { 
        break_duration: 30,
        auto_break: true,
        auto_end: true,
        allow_overtime: true,
        timezone: 'Prague',
        approve_vacations: false,
        daily_team_reports: false
      },
      errors: [],
      logoUrl: '',
      originalLogoUrl: '',
      selectedFile: null,
      uploadError: ''
    };
  },
  created() {
    // Fetch data only if editing an existing company
    if (!this.isNew && this.companyId) {
      this.fetchCompany();
      this.fetchCompanySettings();
    }
  },
  methods: {
    fetchCompany() {
      axios.get(`/companies/${this.companyId}/edit.json`)
        .then(response => {
          this.company = response.data.company;
          this.logoUrl = response.data.company.logo_url;
          this.originalLogoUrl = response.data.company.original_logo_url;
        })
        .catch(error => {
          console.error('Error fetching company:', error);
          this.$emit('error', this.$t('companies.error_loading_workspace_data', 'Nepodařilo se načíst data pracovního prostoru'));
        });
    },
    fetchCompanySettings() {
      axios.get('/company_settings/edit.json')
        .then(response => {
          this.companySettings = response.data.company_setting;
        })
        .catch(error => {
          console.error('Error fetching company settings:', error);
        });
    },
    handleSubmit() {
      if (this.isNew) {
        this.createCompany();
      } else {
        this.updateCompany();
      }
    },
    createCompany() {
      axios.post('/companies.json', { company: this.company })
        .then(response => {
          this.$emit('company-created', this.$t('companies.workspace_created_success', 'Pracovní prostor byl úspěšně vytvořen.'));
        })
        .catch(error => {
          console.error('Error creating company:', error);
          if (error.response && error.response.data && error.response.data.errors) {
             alert(this.$t('validation_error', 'Chyba validace:') + ' ' + Object.entries(error.response.data.errors).map(([k, v]) => `${k} ${v.join(', ')}`).join('\n'));
             this.$emit('error', this.$t('companies.error_creating_workspace_form', 'Nepodařilo se vytvořit pracovní prostor kvůli chybám ve formuláři.'));
          } else {
            this.$emit('error', this.$t('companies.error_creating_workspace', 'Nepodařilo se vytvořit pracovní prostor.'));
          }
        });
    },
    updateCompany() {
      if (!this.companyId) {
        console.error('Company ID is missing for update operation');
        this.$emit('error', this.$t('companies.error_missing_company_id', 'ID pracovního prostoru chybí'));
        return;
      }
      
      axios.put(`/companies/${this.companyId}.json`, { company: this.company })
        .then(response => {
          this.$emit('company-updated', this.$t('companies.workspace_updated_success', 'Pracovní prostor byl úspěšně aktualizován'));
        })
        .catch(error => {
          console.error('Error updating company:', error);
          this.$emit('error', this.$t('companies.error_updating_workspace', 'Nepodařilo se aktualizovat pracovní prostor'));
        });
    },
    updateCompanySettings() {
      const settingsToUpdate = {
        break_duration: this.companySettings.break_duration,
        auto_break: this.companySettings.auto_break,
        auto_end: this.companySettings.auto_end,
        allow_overtime: this.companySettings.allow_overtime,
        timezone: this.companySettings.timezone,
        approve_vacations: this.companySettings.approve_vacations,
        daily_team_reports: this.companySettings.daily_team_reports
      };
      
      axios.put('/company_settings', { company_setting: settingsToUpdate })
        .then(response => {
          this.$emit('settings-updated', this.$t('companies.settings_updated_success', 'Nastavení bylo úspěšně aktualizováno'));
        })
        .catch(error => {
          console.error('Error updating company settings:', error);
          this.$emit('error', this.$t('companies.error_updating_settings', 'Nepodařilo se aktualizovat nastavení'));
        });
    },
    uploadLogo() {
      if (!this.selectedFile || this.isNew) return; 

      if (!this.companyId) {
        console.error('Company ID is missing for logo upload operation');
        this.uploadError = this.$t('companies.error_missing_company_id', 'ID pracovního prostoru chybí');
        this.$emit('error', this.$t('companies.error_missing_company_id', 'ID pracovního prostoru chybí'));
        return;
      }

      const formData = new FormData();
      formData.append('company_logo', this.selectedFile);
      
      axios.post(`/companies/${this.companyId}/upload_logo`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
      .then(response => {
        this.logoUrl = response.data.logo_url;
        this.originalLogoUrl = response.data.original_logo_url;
        this.$emit('logo-updated', this.$t('companies.logo_uploaded_success', 'Logo bylo úspěšně nahráno'));
      })
      .catch(error => {
        console.error('Error uploading logo:', error);
        this.uploadError = this.$t('companies.error_uploading_logo', 'Nepodařilo se nahrát logo');
        this.$emit('error', this.$t('companies.error_uploading_logo', 'Nepodařilo se nahrát logo.')); 
      });
    },
    handleFileChange(event) {
      this.selectedFile = event.target.files[0];
      this.uploadError = ''; 
    }
  }
};
</script>