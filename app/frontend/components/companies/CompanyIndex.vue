<template>
  <div class="p-4 md:p-6">
    <div class="flex justify-between items-center mb-4">
      <div>
        <h2 class="text-xl md:text-2xl font-semibold text-gray-800">{{ $t('companies.workspaces_title', 'Pracovní prostory') }}</h2>
        <p class="text-sm text-gray-500">{{ $t('companies.workspaces_subtitle', 'Firmy a organizace') }}</p>
      </div>
      <button @click="openCreateModal" class="btn btn-primary">{{ $t('companies.new_workspace_button', 'Nový pracovní prostor') }}</button>
    </div>
    
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      <div v-for="role in companyUserRoles" 
           :key="role.company_id" 
           class="card">
        <div class="card-content flex flex-col justify-between h-full">
          <div class="flex items-center mb-3">
            <CompanyLogo 
              :company-id="role.company_id"
              :company-name="role.company.name"
              class="mr-3 flex-shrink-0" 
            />
            <div class="flex-grow">
              <h3 class="font-semibold text-gray-800">{{ role.company.name }}</h3>
              <span class="text-sm text-gray-600">
                {{ role.role.translated_name }}
              </span>
              <div class="mt-1">
                <span v-if="role.company_id === currentTenant" class="badge badge-success">
                  {{ $t('current', 'Aktuální') }}
                </span>
              </div>
            </div>
          </div>
          <div class="card-footer mt-auto pt-3">
            <button v-if="role.company_id !== currentTenant" @click="switchAssignment(role.company_id)" class="btn btn-outline btn-small">
              {{ $t('select_this', 'Vybrat tento') }}
            </button>
            <button v-if="isManager" @click="openEditModal(role.company_id)" class="btn btn-outline btn-small">{{ $t('edit', 'Upravit') }}</button>
            <button @click="confirmLeaveCompany(role.company_id, role.company.name)" class="btn btn-danger-light btn-small ml-auto">{{ $t('disconnect', 'Odpojit se') }}</button>
          </div>
        </div>
      </div>
    </div>

    <!-- Create/Edit Company Modal -->
    <div v-if="showFormModal" class="modal-overlay">
      <div class="modal-container" style="min-height: auto; max-width: 650px;">
        <div class="modal-header">
          <h3 class="text-lg font-semibold">{{ formIsNew ? $t('companies.create_new_workspace_title', 'Vytvořit nový pracovní prostor') : $t('companies.edit_workspace_title', 'Upravit pracovní prostor') }}</h3>
          <button @click="closeFormModal" class="close-btn">&times;</button>
        </div>
        <div class="central-modal-content">
          <CompanyForm 
            :company-id="formCompanyId" 
            :is-new="formIsNew"
            :status-emails="can('can_receive_team_status_emails?')"
            @company-created="handleCompanyCreated"  
            @company-updated="handleCompanyUpdated" 
            @settings-updated="handleSettingsUpdated"
            @logo-updated="handleLogoUpdated" 
            @error="handleError"
          />
        </div>
      </div>
    </div>

    <!-- Leave Company Confirmation Modal -->
    <div v-if="showLeaveConfirm" class="modal-overlay">
       <div class="modal-container" style="min-height: auto; max-width: 500px;">
          <div class="modal-header">
            <h3 class="text-lg font-semibold text-red-600">{{ $t('companies.leave_workspace_warning_title', 'POZOR! Opuštění pracovního prostoru') }}</h3>
             <button @click="cancelLeave" class="close-btn">&times;</button>
          </div>
          <div class="central-modal-content">
            <p class="mb-2">{{ $t('companies.leave_workspace_confirm_text_1', 'Opravdu chcete opustit pracovní prostor') }} <strong>{{ companyToLeave.name }}</strong>?</p>
            <p class="text-sm text-gray-600 mb-4">{{ $t('companies.leave_workspace_confirm_text_2', 'Tato akce je nevratná. Ztratíte přístup ke všem datům v tomto pracovním prostoru.') }}</p>
            <p class="mb-2">{{ $t('companies.enter_to_confirm', 'Pro potvrzení zadejte:') }} <strong class="text-red-700">{{ leaveConfirmationCode }}</strong></p>
            <input 
              v-model="confirmationInput" 
              type="text" 
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 mb-4" 
              :placeholder="$t('companies.enter_confirmation_code_placeholder', 'Zadejte potvrzovací kód')">
             <div class="modal-footer justify-between"> 
              <button @click="cancelLeave" class="btn btn-outline">{{ $t('cancel', 'Zrušit') }}</button>
              <button @click="executeLeave" class="btn btn-danger" :disabled="confirmationInput !== leaveConfirmationCode">
                {{ $t('companies.leave_workspace_button', 'Opustit pracovní prostor') }}
              </button>
            </div>
          </div>
       </div>
    </div>

  </div>
</template>

<script>
import authorizationMixin from '../../mixins/authorizationMixin';
import axios from 'axios';
import AuthService from '../../services/authService';
import CompanyForm from './EditCompany.vue';
import CompanyLogo from './CompanyLogo.vue';

export default {
  mixins: [authorizationMixin],
  components: {
    CompanyForm,
    CompanyLogo
  },
  data() {
    return {
      companyUserRoles: [],
      currentTenant: null,
      showLeaveConfirm: false,
      confirmationInput: '',
      leaveConfirmationCode: '',
      companyToLeave: {
        id: null,
        name: ''
      },
      showFormModal: false,
      formCompanyId: null,
      formIsNew: false
    };
  },
  created() {
    this.fetchCompanyUserRoles();
  },
  methods: {
    fetchCompanyUserRoles() {
      // Use JWT-compatible API endpoint with dual authentication support
      // This endpoint supports JWT-first authentication with session fallback and provides
      // enhanced security by filtering to only active company roles
      axios.get('/api/v1/companies', {
        headers: {
          'Accept': 'application/json'
        }
      })
        .then(response => {
          this.companyUserRoles = response.data.company_user_roles;
          this.currentTenant = response.data.current_tenant;
        })
        .catch(error => {
          console.error('Error fetching company user roles:', error);
          
          // If JWT API fails, we could fallback to the legacy endpoint
          // but this should not be needed since the JWT API supports session fallback
          document.dispatchEvent(new CustomEvent('flashMessage', {
            detail: { 
              text: this.$t('companies.error_loading_workspaces', 'Nepodařilo se načíst pracovní prostory'), 
              type: 'error' 
            }
          }));
        });
    },
    async switchAssignment(companyId) {
      try {
        // Use AuthService for JWT-first company switching with session fallback
        const result = await AuthService.switchCompany(companyId);
        
        if (result.success) {
          // Update the current tenant in our local state
          this.currentTenant = companyId;
          
          // If using JWT, company data is already updated in the store
          // If using session fallback, reload user data to get updated company context
          if (result.authMethod === 'session') {
            await this.$store.dispatch('userStore/fetchUserData');
          } else if (result.authMethod === 'jwt') {
            // For JWT, we might still want to refresh user data to ensure consistency
            // This is especially important for complex authorization state
            try {
              await this.$store.dispatch('userStore/fetchUserData');
            } catch (error) {
              console.warn('Failed to refresh user data after JWT company switch:', error);
              // Don't fail the company switch for this, the token already contains updated company context
            }
          }
          
          // Show success message
          const successMessage = result.message || this.$t('companies.switched_successfully', 'Pracovní prostor byl přepnut');
          document.dispatchEvent(new CustomEvent('flashMessage', {
            detail: { text: successMessage, type: 'success' }
          }));
          
          // Navigate to dashboard in SPA mode
          if (this.$router) {
            // Get current locale from route or cookie
            const locale = this.$route?.params?.locale || document.cookie.match(/locale=([^;]+)/)?.[1] || 'cs';
            this.$router.push(`/${locale}/dashboard`);
          } else {
            // Fallback for non-SPA mode
            window.location.href = window.location.href;
          }
        }
      } catch (error) {
        console.error('Error switching company:', error);
        
        // Extract meaningful error message
        let errorMessage = this.$t('companies.switch_failed', 'Nepodařilo se přepnout pracovní prostor');
        if (error.response?.data?.error) {
          errorMessage = error.response.data.error;
        } else if (error.message) {
          errorMessage = error.message;
        }
        
        document.dispatchEvent(new CustomEvent('flashMessage', {
          detail: { text: errorMessage, type: 'error' }
        }));
      }
    },
    openCreateModal() {
      this.formCompanyId = null;
      this.formIsNew = true;
      this.showFormModal = true;
    },
    openEditModal(companyId) {
      this.formCompanyId = companyId;
      this.formIsNew = false;
      this.showFormModal = true;
    },
    closeFormModal() {
      this.showFormModal = false;
      this.formCompanyId = null;
      this.formIsNew = false;
    },
    handleCompanyCreated(message) {
      document.dispatchEvent(new CustomEvent('flashMessage', {
        detail: { text: message, type: 'success' }
      }));
      this.fetchCompanyUserRoles(); 
      this.closeFormModal();
    },
    handleCompanyUpdated(message) {
      document.dispatchEvent(new CustomEvent('flashMessage', {
        detail: { text: message, type: 'success' }
      }));
      this.fetchCompanyUserRoles(); 
      this.closeFormModal();
    },
    handleSettingsUpdated(message) {
      document.dispatchEvent(new CustomEvent('flashMessage', {
        detail: { text: message, type: 'success' }
      }));
    },
    handleLogoUpdated(message) {
      document.dispatchEvent(new CustomEvent('flashMessage', {
        detail: { text: message, type: 'success' }
      }));
    },
    handleError(errorMessage) {
      document.dispatchEvent(new CustomEvent('flashMessage', {
        detail: { text: errorMessage, type: 'error' }
      }));
    },
    confirmLeaveCompany(companyId, companyName) {
      this.companyToLeave = {
        id: companyId,
        name: companyName
      };
      this.leaveConfirmationCode = this.$t('companies.leave_confirmation_code_prefix', 'ODPOJIT-SE-TED-') + companyId;
      this.confirmationInput = '';
      this.showLeaveConfirm = true;
    },
    cancelLeave() {
      this.showLeaveConfirm = false;
      this.confirmationInput = '';
      this.companyToLeave = { id: null, name: '' };
    },
    executeLeave() {
      if (this.confirmationInput !== this.leaveConfirmationCode) return;
      
      axios.post(`/companies/${this.companyToLeave.id}/leave`)
        .then(response => {
          this.showLeaveConfirm = false;
          this.companyUserRoles = this.companyUserRoles.filter(
            role => role.company_id !== this.companyToLeave.id
          );
          
          if (this.companyToLeave.id === this.currentTenant) {
            window.location.href = '/';
          }
        })
        .catch(error => {
          console.error('Error leaving company:', error);
          let message = this.$t('companies.error_leaving_workspace', 'Nepodařilo se opustit pracovní prostor.');
          if (error.response && error.response.data && error.response.data.message) {
            message = error.response.data.message;
          }
          document.dispatchEvent(new CustomEvent('flashMessage', {
            detail: { text: message, type: 'error' }
          }));
        });
    }
  }
};
</script>

