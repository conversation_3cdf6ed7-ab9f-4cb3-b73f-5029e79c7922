#!/usr/bin/env ruby

# Load Rails environment
require_relative 'config/environment'

ActsAsTenant.with_tenant(Company.first) do
  current_user = User.find_by(email: '<EMAIL>')
  puts "Current user ID: #{current_user.id}"
  
  contracts = Company.first.contracts.where(user_id: current_user.id)
  puts "Found #{contracts.count} contracts for current user:"
  contracts.each do |contract|
    puts "  ID: #{contract.id} - Name: #{contract.first_name} #{contract.last_name} - Email: #{contract.email}"
  end
end