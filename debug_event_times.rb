#!/usr/bin/env ruby

# Load Rails environment
require_relative 'config/environment'

ActsAsTenant.with_tenant(Company.first) do
  events = Event.where(title: ['Test Illness Event', 'Test Vacation Event'])
  events.each do |event|
    puts "Event: #{event.title}"
    puts "  Start: #{event.start_time}"
    puts "  End: #{event.end_time}"
    puts "  Contract ID: #{event.contract_id}"
    puts ""
  end
  
  puts "Query date conditions:"
  start_date = Date.parse('2025-07-21')
  end_date = Date.parse('2025-07-24')
  puts "  start_date.beginning_of_day: #{start_date.beginning_of_day}"
  puts "  end_date.end_of_day: #{end_date.end_of_day}"
  puts ""
  
  # Test the same query from the API
  events_found = Event.joins(:contract)
    .where(contract_id: [5, 191, 80])
    .where('start_time <= ? AND end_time >= ?', end_date.end_of_day, start_date.beginning_of_day)
    .where.not(status: 'rejected')
    .includes(:contract)
  
  puts "Events found by API query: #{events_found.count}"
  events_found.each do |event|
    puts "  - #{event.title}: #{event.start_time} to #{event.end_time}"
  end
  
  puts ""
  puts "Manual condition check:"
  events.each do |event|
    condition1 = event.start_time <= end_date.end_of_day
    condition2 = event.end_time >= start_date.beginning_of_day
    condition3 = [5, 191, 80].include?(event.contract_id)
    condition4 = event.status != 'rejected'
    
    puts "#{event.title}:"
    puts "  start_time <= end_date.end_of_day: #{event.start_time} <= #{end_date.end_of_day} = #{condition1}"
    puts "  end_time >= start_date.beginning_of_day: #{event.end_time} >= #{start_date.beginning_of_day} = #{condition2}"
    puts "  contract_id in [5, 191, 80]: #{event.contract_id} = #{condition3}"
    puts "  status != 'rejected': #{event.status} = #{condition4}"
    puts "  Should be found: #{condition1 && condition2 && condition3 && condition4}"
    puts ""
  end
end