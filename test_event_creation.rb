#!/usr/bin/env ruby

# Load Rails environment
require_relative 'config/environment'

ActsAsTenant.with_tenant(Company.first) do
  contract = Company.first.contracts.first
  if contract
    puts "📝 Creating illness event..."
    
    illness_event = Event.create!(
      contract: contract,
      event_type: :illness,
      start_time: Time.current,
      end_time: Time.current + 8.hours,
      title: "Test Illness Event"
    )
    
    puts "✅ Illness event created:"
    puts "   ID: #{illness_event.id}"
    puts "   Type: #{illness_event.event_type}"
    puts "   Status: #{illness_event.status}"
    puts "   Title: #{illness_event.title}"
    puts ""
    
    puts "📝 Creating vacation event..."
    
    vacation_event = Event.create!(
      contract: contract,
      event_type: :vacation,
      start_time: Time.current + 1.day,
      end_time: Time.current + 2.days,
      title: "Test Vacation Event"
    )
    
    puts "✅ Vacation event created:"
    puts "   ID: #{vacation_event.id}"
    puts "   Type: #{vacation_event.event_type}"
    puts "   Status: #{vacation_event.status}"
    puts "   Title: #{vacation_event.title}"
    
    puts ""
    puts "🔍 Testing conflict detection..."
    
    # Test the conflicts endpoint logic
    contract_ids = [contract.id]
    start_date = Date.current
    end_date = Date.current + 3.days
    
    events = Event.joins(:contract)
      .where(contract_id: contract_ids)
      .where('start_time <= ? AND end_time >= ?', end_date.end_of_day, start_date.beginning_of_day)
      .where.not(status: 'rejected')
      .includes(:contract)
    
    puts "Found #{events.count} events in conflict detection:"
    events.each do |event|
      puts "  - #{event.title} (#{event.event_type}) - Status: #{event.status}"
    end
    
  else
    puts "❌ No contracts found"
  end
end