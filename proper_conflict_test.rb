#!/usr/bin/env ruby

# Load Rails environment
require_relative 'config/environment'

ActsAsTenant.with_tenant(Company.first) do
  puts '🔧 Setting up proper test environment...'
  
  # Find or create current user
  current_user = User.find_by(email: '<EMAIL>')
  if current_user.nil?
    puts "❌ Current user not found"
    exit 1
  end
  
  # Create a contract for current user if they don't have one
  user_contract = Company.first.contracts.find_by(user_id: current_user.id)
  if user_contract.nil?
    puts '📝 Creating contract for current user...'
    user_contract = Company.first.contracts.create!(
      user: current_user,
      first_name: '<PERSON>',
      last_name: 'CodeCraft',
      email: current_user.email,
      contract_type: 'employee',
      valid_since: Date.current,
      valid_through: Date.current + 1.year
    )
    puts "✅ Created contract ID: #{user_contract.id}"
  else
    puts "✅ Found existing contract ID: #{user_contract.id}"
  end
  
  # Use contract ID 5 (existing) for our test events
  test_contract = Company.first.contracts.find_by(id: 5)
  if test_contract.nil?
    puts "❌ Contract ID 5 not found"
    exit 1
  end
  
  # Clean up previous test events first
  Event.where(title: ['Test Illness Event', 'Test Vacation Event']).destroy_all
  puts '🧹 Cleaned up previous test events'
  
  puts "📝 Creating test events with contract ID: #{test_contract.id}..."
  
  illness_event = Event.create!(
    contract: test_contract,
    event_type: :illness,
    start_time: Time.current,
    end_time: Time.current + 8.hours,
    title: "Test Illness Event"
  )
  
  puts "✅ Illness event created:"
  puts "   ID: #{illness_event.id}"
  puts "   Type: #{illness_event.event_type}"
  puts "   Status: #{illness_event.status}"
  puts "   Contract ID: #{illness_event.contract_id}"
  puts ""
  
  vacation_event = Event.create!(
    contract: test_contract,
    event_type: :vacation,
    start_time: Time.current + 1.day,
    end_time: Time.current + 2.days,
    title: "Test Vacation Event"
  )
  
  puts "✅ Vacation event created:"
  puts "   ID: #{vacation_event.id}"
  puts "   Type: #{vacation_event.event_type}"
  puts "   Status: #{vacation_event.status}"
  puts "   Contract ID: #{vacation_event.contract_id}"
  puts ""
  
  # Now test the conflicts API logic with the actual contract ID
  contract_ids = [test_contract.id]
  
  # Add current user's contract ID (like the API does)
  current_user_contract = Company.first.contracts.find_by(user_id: current_user.id)
  if current_user_contract
    contract_ids << current_user_contract.id
    contract_ids.uniq!
  end
  
  puts "🔍 Testing conflict detection with contract_ids: #{contract_ids}"
  
  start_date = Date.current
  end_date = Date.current + 3.days
  
  events = Event.joins(:contract)
    .where(contract_id: contract_ids)
    .where('start_time <= ? AND end_time >= ?', end_date.end_of_day, start_date.beginning_of_day)
    .where.not(status: 'rejected')
    .includes(:contract)
  
  puts "Found #{events.count} events in conflict detection:"
  events.each do |event|
    puts "  - #{event.title} (#{event.event_type}) - Status: #{event.status} - Contract: #{event.contract_id}"
  end
  
  puts ""
  puts "🎯 Now you can test the API endpoint with:"
  puts "   Contract IDs: #{contract_ids.join(',')}"
  puts "   Start Date: #{start_date}"
  puts "   End Date: #{end_date}"
end