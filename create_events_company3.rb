#!/usr/bin/env ruby

# Load Rails environment
require_relative 'config/environment'

company_3 = Company.find(3)
ActsAsTenant.with_tenant(company_3) do
  puts "🔧 Creating test events in Company 3 (#{company_3.name})..."
  puts ""
  
  # Find contract 80 for the current user
  contract_80 = company_3.contracts.find_by(id: 80)
  if contract_80.nil?
    puts "❌ Contract 80 not found in Company 3"
    exit 1
  end
  
  puts "✅ Found contract 80: #{contract_80.first_name} #{contract_80.last_name} - #{contract_80.email}"
  
  # Clean up any existing test events first
  Event.where(title: ['Test Illness Event Company 3', 'Test Vacation Event Company 3']).destroy_all
  puts "🧹 Cleaned up previous test events"
  
  puts "📝 Creating test events with contract ID: #{contract_80.id}..."
  
  illness_event = Event.create!(
    contract: contract_80,
    event_type: :illness,
    start_time: Time.current,
    end_time: Time.current + 8.hours,
    title: "Test Illness Event Company 3"
  )
  
  puts "✅ Illness event created:"
  puts "   ID: #{illness_event.id}"
  puts "   Type: #{illness_event.event_type}"
  puts "   Status: #{illness_event.status}"
  puts "   Contract ID: #{illness_event.contract_id}"
  puts "   Company: #{ActsAsTenant.current_tenant.name}"
  puts ""
  
  vacation_event = Event.create!(
    contract: contract_80,
    event_type: :vacation,
    start_time: Time.current + 1.day,
    end_time: Time.current + 2.days,
    title: "Test Vacation Event Company 3"
  )
  
  puts "✅ Vacation event created:"
  puts "   ID: #{vacation_event.id}"
  puts "   Type: #{vacation_event.event_type}"
  puts "   Status: #{vacation_event.status}"
  puts "   Contract ID: #{vacation_event.contract_id}"
  puts "   Company: #{ActsAsTenant.current_tenant.name}"
  puts ""
  
  # Test conflict detection in the correct context
  contract_ids = [contract_80.id]
  start_date = Date.current
  end_date = Date.current + 3.days
  
  events = Event.joins(:contract)
    .where(contract_id: contract_ids)
    .where('start_time <= ? AND end_time >= ?', end_date.end_of_day, start_date.beginning_of_day)
    .where.not(status: 'rejected')
    .includes(:contract)
  
  puts "🔍 Testing conflict detection with contract_ids: #{contract_ids}"
  puts "Found #{events.count} events in conflict detection:"
  events.each do |event|
    puts "  - #{event.title} (#{event.event_type}) - Status: #{event.status} - Contract: #{event.contract_id}"
  end
  
  puts ""
  puts "🎯 Ready for API testing with:"
  puts "   Contract ID: #{contract_80.id}"
  puts "   Company: #{ActsAsTenant.current_tenant.id} (#{ActsAsTenant.current_tenant.name})"
  puts "   Start Date: #{start_date}"
  puts "   End Date: #{end_date}"
end