#!/usr/bin/env ruby

# Load Rails environment
require_relative 'config/environment'

ActsAsTenant.with_tenant(Company.first) do
  # Find contracts with IDs 1 and 80 that the API is searching for
  contract_1 = Company.first.contracts.find_by(id: 1)
  contract_80 = Company.first.contracts.find_by(id: 80)
  
  puts '🔍 DEBUG Contract availability:'
  puts "  Contract ID 1: #{contract_1 ? 'EXISTS' : 'NOT FOUND'}"
  puts "  Contract ID 80: #{contract_80 ? 'EXISTS' : 'NOT FOUND'}"
  
  # Let's also see what contracts are available
  puts '📋 Available contracts:'
  Company.first.contracts.limit(10).each do |contract|
    puts "  ID: #{contract.id} - User: #{contract.user&.email || 'No user'}"
  end
  
  # Also show current user's contract
  puts ''
  puts '🔍 Current user details:'
  current_user = User.find_by(email: '<EMAIL>')
  if current_user
    puts "  User ID: #{current_user.id}"
    puts "  Email: #{current_user.email}"
    user_contract = Company.first.contracts.find_by(user_id: current_user.id)
    if user_contract
      puts "  Contract ID: #{user_contract.id}"
    else
      puts "  No contract found for current user"
    end
  else
    puts "  User not found"
  end
end